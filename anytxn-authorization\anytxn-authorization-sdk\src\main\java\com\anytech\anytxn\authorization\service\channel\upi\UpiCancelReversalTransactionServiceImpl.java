package com.anytech.anytxn.authorization.service.channel.upi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.enums.AuthMatchResultEnum;
import com.anytech.anytxn.authorization.base.enums.CardClassEnum;
import com.anytech.anytxn.authorization.base.enums.TranTypeDetailEnum;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.mapper.epcc.AuthorizationLogEpccSelfMapper;
import com.anytech.anytxn.authorization.base.domain.model.AuthorizationLog;
import com.anytech.anytxn.authorization.base.domain.model.AuthorizationLogEpcc;
import com.anytech.anytxn.authorization.base.service.auth.*;
import com.anytech.anytxn.authorization.service.auth.LimitRequestPrepareService;
import com.anytech.anytxn.authorization.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.authorization.base.service.upi.IUpiAuthCheckProcessService;
import com.anytech.anytxn.authorization.base.service.upi.IUpiCancelReversalTransactionService;
import com.anytech.anytxn.authorization.base.service.upi.IUpiOriginTransMatchProcessService;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.*;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.monetary.service.CustAccountWriterService;
import com.anytech.anytxn.limit.base.domain.bo.LimitCheckResultBO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthorizationRuleService;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description UPI 撤销冲正交易
 * <AUTHOR>
 * @Date 2018/12/21 11:30 AM
 * Version 1.0
 **/
@Service
@Transactional(rollbackFor = Exception.class)
public class UpiCancelReversalTransactionServiceImpl implements IUpiCancelReversalTransactionService {
    private static final Logger logger = LoggerFactory.getLogger(UpiCancelReversalTransactionServiceImpl.class);
    @Autowired
    private UpiAuthDataUpdateServiceImpl authDataUpdateService;
    @Autowired
    private IUpiOriginTransMatchProcessService upiOriginTransMatchProcessService;
    @Autowired
    private IUpiAuthCheckProcessService authCheckProcessService;
    @Autowired
    private UpiAuthDetailDataModifyServiceImpl upiAuthDetailDataModifyService;
    @Autowired
    private AuthorizationLogEpccSelfMapper authorizationLogEpccSelfMapper;
    @Autowired
    private RuleTransferImpl ruleTransferService;
    @Autowired
    private AuthorizationLogSelfMapper authorizationLogSelfMapper;
    @Autowired
    private IOutstandingTransService outstandingTransService;
    @Autowired
    private IAuthorizationRuleService authorizationRuleService;
    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IPreAuthorizationLogService preAuthorizationLogService;
    @Autowired
    private LimitRequestPrepareService limitRequestPrepareService;
    @Autowired
    private UpiAuthCheckDataPrepareServiceImpl upiAuthCheckDataPrepareService;
    @Autowired
    private SequenceIdGen sequenceIdGen;
    @Resource
    private CustAccountWriterService accountWriterService;

    @Override
    public void cancelReversalProcess(AuthRecordedDTO authRecordedDTO) {
        logger.info("Cancel reversal transaction begin...");
        AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();
        //预授权撤销冲正
        if (authRecordedDTO.getPreAuthComplete() || authRecordedDTO.getPreAuth()) {
            logger.info("Calling upiOriginTransMatchProcessService.preAuthMatchOriginTrans");
            PreAuthorizationLogDTO preAuthorizationLog = upiOriginTransMatchProcessService.preAuthMatchOriginTrans(authRecordedDTO);
            logger.info("Completed upiOriginTransMatchProcessService.preAuthMatchOriginTrans");
            if (preAuthorizationLog != null && com.anytech.anytxn.authorization.base.enums.PreAuthTrancactionStatusEnum.INIT.getCode()
                    .equals(preAuthorizationLog.getPreauthStatusCurr())) {
                logger.info("Pre-authorization reversal matched pre-auth info: logId={}, type={} (1:request 2:cancel 3:complete 4:complete cancel)",
                    preAuthorizationLog.getPreauthLogId(),
                    preAuthorizationLog.getPreauthTxnType());
                if (Arrays.asList("3","4").contains(preAuthorizationLog.getPreauthTxnType())){
                    authRecordedDTO.setPreAuth(false);
                    authRecordedDTO.setPreAuthComplete(true);
                }
                authRecordedDTO.setAuthCardholderBillingAmount(preAuthorizationLog.getCardholderBillingAmount());
                authRecordedDTO.setAuthBillingCurrencyCode(preAuthorizationLog.getBillingCurrencyCode());
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                authRecordedDTO.setAuthOriginalGlobalFlowNumber(preAuthorizationLog.getGlobalFlowNumber());
                authRecordedDTO.setAuthGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                authRecordedDTO.setAuthTransactionTypeTopCode(preAuthorizationLog.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(preAuthorizationLog.getTransactionTypeDetailCode());
                //用作预授权流水表新增
                authRecordedDTO.setAuthAuthCode(preAuthorizationLog.getPreauthCode());
                //用作原预授权请求信息获取
                authRecordedDTO.setAuthAuthIdentificationResponse(preAuthorizationLog.getPreauthCode());
                authRecordedDTO.setLimitUnitList(JSONArray.parseArray(preAuthorizationLog.getLimitUnitJson(), CalculateLimitUnitDTO.class));
                authRecordedDTO.setLimitUnitVersion(preAuthorizationLog.getLimitUnitVersion());
                authRecordedDTO.setTransCtrlUnitId(preAuthorizationLog.getTransCtrlUnitId());
                if (authRecordedDTO.getPreAuthComplete()){
                    AuthorizationLog authorizationLog = authorizationLogSelfMapper.selectByGlobalFlowNumber(preAuthorizationLog.getGlobalFlowNumber());
                    authRecordedDTO.setPostingTransactionCode(authorizationLog.getPostingTransactionCode());
                    authRecordedDTO.setPostingTransactionCodeRev(authorizationLog.getPostingTransactionCodeRev());
                }
                try {
                    logger.info("Calling authCheckProcessService.authCheck");
                    authCheckProcessService.authCheck(authRecordedDTO);
                    logger.info("Completed authCheckProcessService.authCheck");
                    return;
                } catch (IOException e) {
                    logger.error("Cancel reversal transaction authorization processing exception:", e);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_AUTH_CHECK_EROR, e);
                }
            } else {
                logger.error("Pre-authorization cancel reversal did not match original transaction");
                authRecordedDTO.setAuthResponseCode("25");
                authRecordedDTO.setAuthResponseReasonCode("00");
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.ERROR_STATUS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
                logger.info("Calling upiAuthDetailDataModifyService.addAuthorizationLog");
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
                logger.info("Completed upiAuthDetailDataModifyService.addAuthorizationLog");
                return;
            }
        } else {
            //调用匹配接口
            Map<String, AuthorizationLogDTO> originalMatchSignMap = upiOriginTransMatchProcessService.matchOriginTrans(authRecordedDTO);
            originalMatchSignMap.forEach((key, value) -> {
                if (String.valueOf(AuthConstans.ONE).equals(key)) {
                    logger.info("Reversal transaction match result: size={}", originalMatchSignMap != null ? originalMatchSignMap.size() : 0);
                    //授权接口赋值
                    authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                    authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                    authRecordedDTO.setAuthGlobalFlowNumber(value.getGlobalFlowNumber());
                    authRecordedDTO.setAuthOriginalGlobalFlowNumber(value.getOriginalGlobalFlowNumber());
                    authRecordedDTO.setAuthTransactionTypeTopCode(value.getTransactionTypeTopCode());
                    authRecordedDTO.setAuthTransactionTypeDetailCode(value.getTransactionTypeDetailCode());
                    authRecordedDTO.setAuthAuthCode(value.getAuthCode());
                    authRecordedDTO.setAuthCustomerId(value.getCustomerId());
                    authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
                    //实时入账交易码,实时入账交易码(冲正或者撤销用)
                    authRecordedDTO.setPostingTransactionCode(value.getPostingTransactionCode());
                    authRecordedDTO.setPostingTransactionCodeRev(value.getPostingTransactionCodeRev());
                    authRecordedDTO.setAuthCardholderBillingAmount(value.getCardholderBillingAmount());
                    authRecordedDTO.setAuthBillingCurrencyCode(value.getBillingCurrencyCode());
                    //数据更新
                    try {
                        authCheckProcessService.authCheck(authRecordedDTO);
                    } catch (IOException e) {
                        logger.error("Authorization data preparation and update exception:", e);
                    }
                }
                if (AuthConstans.II.equals(key) || AuthConstans.III.equals(key)) {
                    authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ERROR);
                    authDataUpdateService.authDataUpdate(authorizationCheckProcessingPayload);
                }
            });
        }
        logger.info("Cancel reversal transaction end...");
    }

    /**
     * 撤销和冲正处理 1:匹配到原交易,并且原交易授权成功;2:匹配到原交易，并且原交易授权失败;3:未匹配到原交易
     * @param authRecordedDTO {@link AuthRecordedDTO }
     * authTransactionAmount     4号域原交易金额
     * authTransactionAmount	 交易金额    4号域
     *
     * authTransactionCurrencyCode	49号域原交易币种
     * authTransactionCurrencyCode	  交易币种    49号域
     *
     * originalTransmissionTime	  F090.3 原交易时间
     * transmissionTime	         交易时间    7号域
     *
     * originalAcquiringIdentificationCode	 F090.4 原机构受理标识码值
     * acquiringIdentificationCode 机构受理标识码值 32号域
     *
     * originalForwardingIdentificationCode F090.5 原发送机构标识码
     * forwardingIdentificationCode  发送机构标识码  33号域
     *
     * originalSystemTraceAuditNumber    F090.2  原系统跟踪号
     * systemTraceAuditNumber	 系统跟踪号   11号域
     */
    @Override
    public void cancelAndReverse(AuthRecordedDTO authRecordedDTO) throws IOException {
        if (authRecordedDTO.getPreAuth()){
            preAuthCancelHandler(authRecordedDTO);
        }else if (authRecordedDTO.getPreAuthComplete()){
            preAuthCompleteCancelHandler(authRecordedDTO);
        } else if (authRecordedDTO.getAuthProcessingCode() != null && (authRecordedDTO.getAuthProcessingCode().startsWith("89") || authRecordedDTO.getAuthProcessingCode().startsWith("92"))) {
            //建立\解除委托冲正处理
            reverseAuthorize(authRecordedDTO);
        } else {
            //@todo 网联的撤销暂时按照银联的处理 页面的撤销暂时无法使用，待调整
            //epccAuthCancelHandler(authRecordedDTO);
            normalAuthCancelHandler(authRecordedDTO);
        }
    }

    /**
     * 建立委托冲正
     * @param authRecordedDTO
     */
    private void reverseAuthorize(AuthRecordedDTO authRecordedDTO) throws IOException {
        //普通授权撤销
        Map<String, AuthorizationLogDTO> matchResult = upiOriginTransMatchProcessService.matchAuthOriginalTrans(authRecordedDTO);
        logger.info("Establish delegate reversal original transaction match result: {}", matchResult);
        //结果只有一条
        Optional<Map.Entry<String, AuthorizationLogDTO>> first = matchResult.entrySet().stream().findFirst();
        if (first.isPresent()) {
            String key = first.get().getKey();
            AuthorizationLogDTO authorizationLog = first.get().getValue();
            if (AuthMatchResultEnum.MATCH_SUCCESS.getCode().equals(key)) {
                logger.info("Matched original transaction with success status: {}", authorizationLog.getAuthLogId());
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                authRecordedDTO.setAuthGlobalFlowNumber(authorizationLog.getGlobalFlowNumber());
                authRecordedDTO.setAuthOriginalGlobalFlowNumber(authorizationLog.getGlobalFlowNumber());
                authRecordedDTO.setAuthTransactionTypeTopCode(authorizationLog.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(authorizationLog.getTransactionTypeDetailCode());
                //返回系统自动生成的授权码
                authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                authRecordedDTO.setAuthCustomerId(authorizationLog.getCustomerId());
                //数据处理
                authCheckProcessService.authCheck(authRecordedDTO);
            } else {
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ERROR);
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            }
        }
    }

    /**
     * 解除委托冲正
     *
     * @param authRecordedDTO
     */
    private void reverseUnAuthorize(AuthRecordedDTO authRecordedDTO) {

    }

    private void normalAuthCancelHandler(AuthRecordedDTO authRecordedDTO) throws IOException {
        //普通授权撤销
        Map<String, AuthorizationLogDTO> matchResult = upiOriginTransMatchProcessService.matchOriginTrans(authRecordedDTO);
        logger.info("Cancel or reversal match result: {}", matchResult.keySet());
        //结果只有一条
        Optional<Map.Entry<String, AuthorizationLogDTO>> first = matchResult.entrySet().stream().findFirst();
        if (first.isPresent()){
            String key = first.get().getKey();
            AuthorizationLogDTO authorizationLog = first.get().getValue();
            if(AuthMatchResultEnum.MATCH_REVACATED.getCode().equals(key)){
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ALREADY_CANCEL);
                authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
                return;
            }
            if (AuthMatchResultEnum.MATCH_SUCCESS.getCode().equals(key)) {
                logger.info("Matched original transaction: {}", authorizationLog.getAuthLogId());
                authRecordedDTO.setAuthTransactionSourceCode(authorizationLog.getAuthTransactionSourceCode());
                if (overLimitDays(authRecordedDTO,authorizationLog)){
                    authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ALREADY_CANCEL);
                    authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                    upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
                    return;
                }
                //true 拒绝
                if (originalTransReject(authRecordedDTO,authorizationLog.getTransactionTypeTopCode(),authorizationLog.getTransactionTypeDetailCode())){
                    return;
                }
                authRecordedDTO.setAuthCardholderBillingAmount(authorizationLog.getCardholderBillingAmount());
                authRecordedDTO.setAuthBillingCurrencyCode(authorizationLog.getBillingCurrencyCode());
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
                    authRecordedDTO.setAuthOriginalGlobalFlowNumber(authorizationLog.getGlobalFlowNumber());
                }
                if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
                    authRecordedDTO.setAuthGlobalFlowNumber(authorizationLog.getGlobalFlowNumber());
                    authRecordedDTO.setAuthOriginalGlobalFlowNumber(authorizationLog.getGlobalFlowNumber());
                }
                authRecordedDTO.setAuthTransactionTypeTopCode(authorizationLog.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(authorizationLog.getTransactionTypeDetailCode());
                //返回系统自动生成的授权码
                authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                authRecordedDTO.setAuthCustomerId(authorizationLog.getCustomerId());
                //实时入账交易码,实时入账校验码(撤销或者冲正用)
                authRecordedDTO.setPostingTransactionCode(authorizationLog.getPostingTransactionCode());
                authRecordedDTO.setPostingTransactionCodeRev(authorizationLog.getPostingTransactionCodeRev());
                //数据处理
                authCheckProcessService.authCheck(authRecordedDTO);
            }else {
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ERROR);
                //贷记交易退货处理
                if(TranTypeDetailEnum.DEBIT_TXN.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())){
                    authRecordedDTO.setAuthResponseReasonCode("21");
                    authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.NOT_MATCH_ORIGINAL_TRANS);
                }
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            }
        }
    }

    private boolean originalTransReject(AuthRecordedDTO authRecordedDTO, String topCode, String detailCode) {
        /*
         * F22（服务点输入方式）=02* 或者90* and
         * F60.2.3（IC卡条件代码）=1或者2 and
         * F60.2.2（终端读取能力）=5或者6 and
         * F33（发送机构标识码）=00010344
         * 返回45
         */
        boolean cardCode = Objects.equals("02",authRecordedDTO.getAuthServicePointCardCode()) ||
                Objects.equals("90",authRecordedDTO.getAuthServicePointCardCode());
        boolean forwardCode = !Objects.equals(authRecordedDTO.getAuthForwardingIdentificationCode(), "00010344");
        boolean b2_2 = false;
        boolean b2_3 = false;
        String field60TxnData = authRecordedDTO.getField60TxnData();
        if (StringUtils.length(field60TxnData) >= 7){
            String s2_2 = field60TxnData.substring(5, 6);
            b2_2 = Objects.equals(s2_2, "5") || Objects.equals(s2_2, "6");
            String s2_3 = field60TxnData.substring(6, 7);
            b2_3 = Objects.equals(s2_3, "1") || Objects.equals(s2_3, "2");
        }
        if (cardCode && forwardCode && b2_2 && b2_3){
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_LOCK);
            authRecordedDTO.setAuthResponseReasonCode("21");
            authRecordedDTO.setAuthTransactionTypeTopCode(topCode);
            authRecordedDTO.setAuthTransactionTypeDetailCode(detailCode);
            upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            return true;
        }
        return false;
    }


    private boolean overLimitDays(AuthRecordedDTO authRecordedDTO, AuthorizationLogDTO authorizationLog) {
        OutstandingTransactionDTO transaction = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authorizationLog.getGlobalFlowNumber(),authorizationLog.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(authorizationLog.getOrganizationNumber());
        CardProductInfoResDTO cardProductInfo = cardProductInfoService.findByOrgAndProductNum(
                authorizationLog.getOrganizationNumber(), transaction.getProductNumber());
        AnyTxnAuthException.executeEmptyQuery(cardProductInfo,CardProductInfoResDTO.class);
        authRecordedDTO.setCardClass(CardClassEnum.getCupCardClassCode(cardProductInfo.getCardClass()));
        // 卡授权参数表
        AuthorizationRuleDTO authorizationRuleDTO = authorizationRuleService
                .findAuthorizationByTableId(cardProductInfo.getAuthCtlTableId(),authorizationLog.getOrganizationNumber());
        //原交易日期
        LocalDateTime originalTransTime = transaction.getTransactionTime();
        Integer removalLimitDays = Optional.ofNullable(authorizationRuleDTO.getRemovalLimitDays()).orElse(0);
        LocalDateTime localDateTime = ApplicationManager.getTransTimeOfUpi(organizationInfo, authRecordedDTO);
        //超过规定的天数则拒绝撤销
        int days = Period.between(originalTransTime.toLocalDate(), localDateTime.toLocalDate()).getDays();
        if (days < 0){
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_DATE_FORMAT_ERROR, AuthRepDetailEnum.ILL_DF);
        }
        return days > removalLimitDays;
    }

    private void epccAuthCancelHandler(AuthRecordedDTO authRecordedDTO) throws IOException {
        if (AuthTransactionSourceCodeEnum.ECPP.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
            // EPCC 匹配
            logger.info("NUCC cancel processing started");
            AuthorizationLogEpcc authorizationLogEpcc = authorizationLogEpccSelfMapper.selectByTrxId(authRecordedDTO.getOriTrxId());
            if (authorizationLogEpcc != null && AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode()
                    .equals(authorizationLogEpcc.getTrancactionStatus())) {
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                authRecordedDTO.setAuthTransactionTypeTopCode(authorizationLogEpcc.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(authorizationLogEpcc.getTransactionTypeDetailCode());
                authRecordedDTO.setAuthOriginalGlobalFlowNumber(authorizationLogEpcc.getGlobalFlowNumber());
                //授权码赋值 用作预授权流水
                authRecordedDTO.setAuthAuthCode(authorizationLogEpcc.getAuthCode());
                //实时入账交易码,实时入账校验码(撤销或者冲正用)
                authRecordedDTO.setPostingTransactionCode(authorizationLogEpcc.getPostingTransactionCode());
                authRecordedDTO.setPostingTransactionCodeRev(authorizationLogEpcc.getPostingTransactionCodeRev());
                try {
                    authCheckProcessService.authCheck(authRecordedDTO);
                } catch (IOException e) {
                    logger.error("IOException:", e);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_ISO_EXCEPTION, e);
                }
            } else {
                logger.error("NUCC transaction did not match original transaction");
                authRecordedDTO.setAuthResponseCode("25");
                authRecordedDTO.setAuthResponseReasonCode("00");
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.ERROR_STATUS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(ReversalTypeEnum.REVOCATION_TRANS.getCode());
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            }
        }
    }


    private void preAuthCompleteCancelHandler(AuthRecordedDTO authRecordedDTO) throws IOException {
        PreAuthorizationLogDTO preAuthorizationLog = matchOriginalAuthLog(authRecordedDTO);
        //未匹配到原交易
        if (preAuthorizationLog == null || !com.anytech.anytxn.authorization.base.enums.PreAuthTrancactionStatusEnum.INIT.getCode()
                .equals(preAuthorizationLog.getPreauthStatusCurr())){
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ERROR);
            authRecordedDTO.setAuthResponseReasonCode("00");
            authRecordedDTO.setAuthTransactionTypeCode(ReversalTypeEnum.REVOCATION_TRANS.getCode());
            upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            return;
        }
        logger.info("Pre-authorization completion cancel transaction: {}", preAuthorizationLog.getPreauthLogId());
        authRecordedDTO.setAuthCardholderBillingAmount(preAuthorizationLog.getCardholderBillingAmount());
        authRecordedDTO.setAuthBillingCurrencyCode(preAuthorizationLog.getBillingCurrencyCode());
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode(preAuthorizationLog.getTransactionTypeTopCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(preAuthorizationLog.getTransactionTypeDetailCode());
        authRecordedDTO.setAuthOriginalGlobalFlowNumber(preAuthorizationLog.getGlobalFlowNumber());
        //全局流水号 每一次交易都是最新的
        authRecordedDTO.setAuthGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        //授权码赋值 用作预授权流水
        authRecordedDTO.setAuthAuthCode(preAuthorizationLog.getPreauthCode());
        //用作原预授权请求信息获取
        authRecordedDTO.setAuthAuthIdentificationResponse(preAuthorizationLog.getPreauthCode());
        authRecordedDTO.setLimitUnitList(JSONArray.parseArray(preAuthorizationLog.getLimitUnitJson(), CalculateLimitUnitDTO.class));
        authRecordedDTO.setLimitUnitVersion(preAuthorizationLog.getLimitUnitVersion());
        authRecordedDTO.setTransCtrlUnitId(preAuthorizationLog.getTransCtrlUnitId());
        //如果是预授权完成 并且是撤销交易则 从授权流水表中获取, 预授权完成时的入账交易码
        AuthorizationLog authorizationLog = authorizationLogSelfMapper.selectByGlobalFlowNumber(preAuthorizationLog.getGlobalFlowNumber());
        authRecordedDTO.setPostingTransactionCode(authorizationLog.getPostingTransactionCode());
        authRecordedDTO.setPostingTransactionCodeRev(authorizationLog.getPostingTransactionCodeRev());
        authCheckProcessService.authCheck(authRecordedDTO);
        //预授权完成的撤销完成后 需要恢复原预授权额度
        if (AuthCheckManager.upiIsSuccess(authRecordedDTO)){
            PreAuthorizationLogDTO originalPreAuthLog = preAuthorizationLogService
                    .getByCardNumAndAuthCodeAndTransType(authRecordedDTO.getAuthCardNumber()
                            ,authRecordedDTO.getAuthAuthIdentificationResponse()
                            , com.anytech.anytxn.authorization.base.enums.PreAuthTransTypeEnum.PRE_REQUEST.getCode());
            logger.info("Pre-authorization completion cancel got original pre-auth transaction: {}", originalPreAuthLog.getPreauthLogId());
            //额度检查 先刷新缓存额度信息
            cleanLimitCacheData();
            AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload = upiAuthCheckDataPrepareService.prepareAuthData(authRecordedDTO);
            authRecordedDTO.setAuthCardholderBillingAmount(originalPreAuthLog.getCardholderBillingAmount());
            LimitCheckResultBO limitCheckResult = limitRequestPrepareService.prepareLimitCheck(authorizationCheckProcessingPayload);
            logger.info("Pre-authorization completion cancel limit check result: {}", JacksonUtils.toJsonStr(limitCheckResult));
            String typeCode = authRecordedDTO.getAuthTransactionTypeCode();
            if (limitCheckResult.getCheckOver()){
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
                authRecordedDTO.setLimitUnitList(BeanMapping.copyList(limitCheckResult.getLimitUnitList(), CalculateLimitUnitDTO.class));
                limitRequestPrepareService.updateLimit(authorizationCheckProcessingPayload);
                //将新的额度占用规则重新赋值
                originalPreAuthLog.setLimitUnitJson(JacksonUtils.toJsonStr(limitCheckResult.getLimitUnitList()));
                preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(originalPreAuthLog);
                commitLimitWrite();
                authRecordedDTO.setAuthTransactionTypeCode(typeCode);
            }else {
                //恢复后的额度还不够 则需要回滚原恢复逻辑，包含账务
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.AWAILABLE_CREDIT);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.AVAILABLE_LIMIT_ERROR);
            }
        }
    }

    private void cleanLimitCacheData(){
        if (CustAccountBO.threadCustAccountBO.get() != null) {
            CustAccountBO.threadCustAccountBO.get().getLimitBO().getLimitCustUsedInfoMap().clear();
            CustAccountBO.threadCustAccountBO.get().getLimitBO().getLimitCustCreditInfoMap().clear();
            CustAccountBO.threadCustAccountBO.get().getLimitBO().getInsertLimitSynRequestLog().clear();
        }
    }

    private void commitLimitWrite() {
        if (CustAccountBO.threadCustAccountBO.get() != null) {
            accountWriterService.writeLimitCustUsedInfo(CustAccountBO.threadCustAccountBO.get().getLimitBO());
        }
    }

    private void preAuthCancelHandler(AuthRecordedDTO authRecordedDTO) throws IOException {
        /*
            预授权撤销时，如果上送了F90，则优先按照F90优先匹配；
            如果未上送F90，则按照卡号+交易金额+授权码进行匹配
         */
        PreAuthorizationLogDTO preAuthorizationLog;
        if (StringUtils.isBlank(authRecordedDTO.getAuthOriginalTransmissionTime())){
            preAuthorizationLog = matchLikeOriginalAuthLog(authRecordedDTO);
        }else {
            preAuthorizationLog = matchOriginalAuthLog(authRecordedDTO);
        }
        if (preAuthorizationLog == null || !com.anytech.anytxn.authorization.base.enums.PreAuthTrancactionStatusEnum.INIT.getCode()
                .equals(preAuthorizationLog.getPreauthStatusCurr())){
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ERROR);
            authRecordedDTO.setAuthResponseReasonCode("00");
            authRecordedDTO.setAuthTransactionTypeCode(ReversalTypeEnum.REVOCATION_TRANS.getCode());
            upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            return;
        }
        logger.info("Pre-authorization cancel transaction: {}", preAuthorizationLog.getPreauthLogId());
        //true 拒绝
        if (originalTransReject(authRecordedDTO,preAuthorizationLog.getTransactionTypeTopCode(),preAuthorizationLog.getTransactionTypeDetailCode())){
            return;
        }
        authRecordedDTO.setAuthCardholderBillingAmount(preAuthorizationLog.getCardholderBillingAmount());
        authRecordedDTO.setAuthBillingCurrencyCode(preAuthorizationLog.getBillingCurrencyCode());
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode(preAuthorizationLog.getTransactionTypeTopCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(preAuthorizationLog.getTransactionTypeDetailCode());
        authRecordedDTO.setAuthGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        authRecordedDTO.setAuthOriginalGlobalFlowNumber(preAuthorizationLog.getGlobalFlowNumber());
        //授权码赋值 用作预授权流水
        authRecordedDTO.setAuthAuthCode(preAuthorizationLog.getPreauthCode());
        //用作原预授权请求信息获取
        authRecordedDTO.setAuthAuthIdentificationResponse(preAuthorizationLog.getPreauthCode());
        authRecordedDTO.setLimitUnitList(JSONArray.parseArray(preAuthorizationLog.getLimitUnitJson(), CalculateLimitUnitDTO.class));
        authRecordedDTO.setLimitUnitVersion(preAuthorizationLog.getLimitUnitVersion());
        authRecordedDTO.setTransCtrlUnitId(preAuthorizationLog.getTransCtrlUnitId());
        authCheckProcessService.authCheck(authRecordedDTO);
    }

    private PreAuthorizationLogDTO matchLikeOriginalAuthLog(AuthRecordedDTO authRecordedDTO) {
        return upiOriginTransMatchProcessService.matchLikeOriginalAuthLog(authRecordedDTO);
    }

    private PreAuthorizationLogDTO matchOriginalAuthLog(AuthRecordedDTO authRecordedDTO){
        return upiOriginTransMatchProcessService.preAuthMatchOriginTrans(authRecordedDTO);
    }

    /**
     *  退货逻辑处理
     * @param authRecordedDTO {@link AuthRecordedDTO }
     */
    @Override
    public void refundsTrans(AuthRecordedDTO authRecordedDTO) {
        //普通授权撤销
        Map<String, AuthorizationLogDTO> matchResult = upiOriginTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO);
        logger.info("Refund match result: size={}", matchResult != null ? matchResult.size() : 0);
        //结果只有一条
        Optional<Map.Entry<String, AuthorizationLogDTO>> first = matchResult.entrySet().stream().findFirst();
        if (first.isPresent()){
            String key = first.get().getKey();
            AuthorizationLogDTO authorizationLog = first.get().getValue();
            if (AuthMatchResultEnum.MATCH_SUCCESS.getCode().equals(key)) {
                logger.info("Refund matched original transaction: {}", authorizationLog.getAuthLogId());
                OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                        authorizationLog.getGlobalFlowNumber(), authRecordedDTO.getOrganizationNumber());
                if(null != outstandingTransactionDTO){
                    outstandingTransactionDTO.setAmountReturned(outstandingTransactionDTO.getAmountReturned()==null?BigDecimal.ZERO :outstandingTransactionDTO.getAmountReturned());
                    authRecordedDTO.setRefundedAmount(outstandingTransactionDTO.getAmountReturned());
                    if( authorizationLog.getTransactionAmount().compareTo(authRecordedDTO.getAuthTransactionAmount().add(outstandingTransactionDTO.getAmountReturned()))<0){
                       logger.info("Refunded amount plus current refund amount exceeds original transaction total amount: authLogId={}", authorizationLog.getAuthLogId());
                       authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.ORIGINAL_TRANS_STATUS_OVER.getCode());
                       authRecordedDTO.setAuthTrancactionStatus("4");
                       authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                       upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
                       return;
                   }
                }
                authRecordedDTO.setAuthBillingCurrencyCode(authorizationLog.getBillingCurrencyCode());
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                authRecordedDTO.setAuthOriginalGlobalFlowNumber(authorizationLog.getGlobalFlowNumber());
                authRecordedDTO.setAuthTransactionTypeTopCode(authorizationLog.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(authorizationLog.getTransactionTypeDetailCode());
                //返回系统自动生成的授权码
                authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                authRecordedDTO.setAuthCustomerId(authorizationLog.getCustomerId());
                //实时入账交易码,实时入账校验码(撤销或者冲正用)
                authRecordedDTO.setPostingTransactionCode(authorizationLog.getPostingTransactionCode());
                authRecordedDTO.setPostingTransactionCodeRev(authorizationLog.getPostingTransactionCodeRev());
                //本质是数据处理
                try {
                    authCheckProcessService.authCheck(authRecordedDTO);
                } catch (IOException e) {
                    logger.error("Refund data update failed", e);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_ISO_EXCEPTION, e);
                }
            }else if(AuthMatchResultEnum.MATCH_REVACATED.getCode().equals(key)){
                logger.info("Refund original transaction already refunded, returning 25");
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.ORIGINAL_TRANS_ALREADY_CANCEL.getCode());
                authRecordedDTO.setAuthTrancactionStatus("4");
                authRecordedDTO.setAuthAuthCode(authorizationLog.getAuthCode());
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            }else{
                logger.info("Refund did not match original transaction or original transaction is in error status");
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.TWENTY_FIVE.getCode());
                authRecordedDTO.setAuthTrancactionStatus("4");
                //贷记交易退货处理
                if(TranTypeDetailEnum.DEBIT_TXN.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())){
                    authRecordedDTO.setAuthResponseCode("95");
                    authRecordedDTO.setAuthResponseReasonCode("21");
                    authRecordedDTO.setAuthTrancactionStatus("4");
                }
                upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            }
        }else{
            //没有找到原交易信息 匹配交易识别
            Map<String, String> stringStringMap = ruleTransferService.getUpiTransIdentifyCheckRule(OrgNumberUtils.getOrg(), AuthThreadLocalManager.getIso8583dtoThreadLocal());
            if (stringStringMap != null) {
                String authTransactionTypeTopCode = stringStringMap.get(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE);
                authRecordedDTO.setAuthTransactionTypeTopCode(authTransactionTypeTopCode);
                String authTransactionTypeDetailCode = stringStringMap.get(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE);
                authRecordedDTO.setAuthTransactionTypeDetailCode(authTransactionTypeDetailCode);
                String postingTransactionCode = stringStringMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE);
                authRecordedDTO.setPostingTransactionCode(postingTransactionCode);
                String postingTransactionCodeDev =
                        stringStringMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE_DEV);
                authRecordedDTO.setPostingTransactionCodeRev(postingTransactionCodeDev);
            }
            try {
                authCheckProcessService.authCheck(authRecordedDTO);
            } catch (IOException e) {
                logger.error("Original transaction not found, transaction identification matching, refund data update failed", e);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_ISO_EXCEPTION, e);
            }
        }
    }
}
